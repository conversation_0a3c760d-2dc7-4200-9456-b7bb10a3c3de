#!/bin/bash

# Script to optimize SSHFS mount for large dataset training
# Run with sudo: sudo ./optimize_sshfs_for_training.sh

set -e

echo "=== SSHFS Optimization for Large Dataset Training ==="
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Error: This script must be run as root (use sudo)"
    exit 1
fi

# Backup current fstab
echo "1. Backing up current fstab..."
cp /etc/fstab /etc/fstab.backup.$(date +%Y%m%d_%H%M%S)
echo "   Backup created: /etc/fstab.backup.$(date +%Y%m%d_%H%M%S)"

# Show current mount
echo ""
echo "2. Current /s2 mount:"
mount | grep /s2 || echo "   /s2 not currently mounted"

# Prepare optimized fstab entry
echo ""
echo "3. Preparing optimized fstab entry..."

OPTIMIZED_ENTRY="root@*************:/mnt /s2 fuse.sshfs _netdev,port=2325,IdentityFile=/root/.ssh/id_storage2_164,allow_other,default_permissions,reconnect,x-systemd.automount,noauto,uid=0,gid=1002,umask=002,cache=yes,kernel_cache,compression=no,large_read,big_writes,max_read=131072,max_write=131072,readahead_size=1048576,ServerAliveInterval=15,ServerAliveCountMax=3 0 0"

# Remove old entry and add new one
echo "   Updating /etc/fstab..."
grep -v "*************:/mnt /s2" /etc/fstab > /etc/fstab.tmp || true
echo "$OPTIMIZED_ENTRY" >> /etc/fstab.tmp
mv /etc/fstab.tmp /etc/fstab

echo "   ✓ Updated /etc/fstab with optimized SSHFS options"

# Optimize SSH client config
echo ""
echo "4. Optimizing SSH client configuration..."

SSH_CONFIG="/root/.ssh/config"
mkdir -p /root/.ssh

# Create or update SSH config
cat > "$SSH_CONFIG" << 'EOF'
Host *************
    Port 2325
    IdentityFile /root/.ssh/id_storage2_164
    Compression no
    TCPKeepAlive yes
    ServerAliveInterval 15
    ServerAliveCountMax 3
    ControlMaster auto
    ControlPath /tmp/ssh-%r@%h:%p
    ControlPersist 1h
EOF

chmod 600 "$SSH_CONFIG"
echo "   ✓ Updated SSH client configuration"

# Optimize system network settings
echo ""
echo "5. Optimizing system network settings..."

# Backup current sysctl.conf
cp /etc/sysctl.conf /etc/sysctl.conf.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# Add network optimizations if not already present
if ! grep -q "net.core.rmem_max = 134217728" /etc/sysctl.conf; then
    cat >> /etc/sysctl.conf << 'EOF'

# SSHFS optimizations for large dataset training
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
EOF
    echo "   ✓ Added network buffer optimizations to /etc/sysctl.conf"
    
    # Apply sysctl changes
    sysctl -p > /dev/null
    echo "   ✓ Applied network optimizations"
else
    echo "   ✓ Network optimizations already present"
fi

# Optimize file descriptor limits
echo ""
echo "6. Optimizing file descriptor limits..."

LIMITS_FILE="/etc/security/limits.conf"
if ! grep -q "nofile 65536" "$LIMITS_FILE"; then
    cat >> "$LIMITS_FILE" << 'EOF'

# File descriptor limits for large dataset training
* soft nofile 65536
* hard nofile 65536
EOF
    echo "   ✓ Updated file descriptor limits"
else
    echo "   ✓ File descriptor limits already optimized"
fi

# Remount with new options
echo ""
echo "7. Remounting /s2 with optimized options..."

# Unmount if currently mounted
if mount | grep -q "/s2"; then
    echo "   Unmounting current /s2..."
    umount /s2 || echo "   Warning: Could not unmount /s2 (may not be mounted)"
fi

# Mount with new options
echo "   Mounting /s2 with optimized options..."
mount /s2

if mount | grep -q "/s2"; then
    echo "   ✓ Successfully mounted /s2 with optimized options"
    echo ""
    echo "Current mount options:"
    mount | grep /s2
else
    echo "   ✗ Failed to mount /s2"
    exit 1
fi

# Test basic connectivity
echo ""
echo "8. Testing optimized mount..."

if [ -d "/s2/mds_300BT_corpus" ]; then
    echo "   ✓ Dataset directory accessible"
    
    # Test directory listing performance
    echo "   Testing directory listing performance..."
    time timeout 30 ls /s2/mds_300BT_corpus/ > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "   ✓ Directory listing successful"
    else
        echo "   ⚠ Directory listing slow or failed"
    fi
    
    # Test file access
    if [ -f "/s2/mds_300BT_corpus/train/index.json" ]; then
        echo "   Testing file access performance..."
        time timeout 30 head -1 /s2/mds_300BT_corpus/train/index.json > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "   ✓ File access successful"
        else
            echo "   ⚠ File access slow or failed"
        fi
    fi
else
    echo "   ⚠ Dataset directory not found - check mount path"
fi

echo ""
echo "=== OPTIMIZATION COMPLETE ==="
echo ""
echo "Summary of optimizations applied:"
echo "✓ SSHFS mount options optimized for large datasets"
echo "✓ SSH client configuration optimized"
echo "✓ Network buffer sizes increased"
echo "✓ File descriptor limits increased"
echo "✓ Mount remounted with new options"
echo ""
echo "You can now test training with:"
echo "  ./setup_large_dataset_training.sh"
echo "  python main.py yamls/main/flex-bert-modernbert-base-edu-fw-fw2-fw-300BT-tokenizer-65k-train-only.yaml"
echo ""
echo "If you still experience issues, consider:"
echo "1. Using the ultra-aggressive caching options (see optimized_sshfs_setup.md)"
echo "2. Copying a subset of data locally for testing"
echo "3. Using a faster network connection to the storage server"

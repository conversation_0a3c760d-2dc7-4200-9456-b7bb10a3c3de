#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to decompress the test split of the 300BT dataset to enable non-streaming mode.
This allows us to avoid distributed barrier issues in the eval loader.
"""

import os
import zstandard as zstd
import shutil
from pathlib import Path
import argparse

def decompress_file(compressed_path, output_path):
    """Decompress a single .zstd file"""
    print(f"Decompressing {compressed_path} -> {output_path}")
    
    with open(compressed_path, 'rb') as compressed_file:
        dctx = zstd.ZstdDecompressor()
        with open(output_path, 'wb') as output_file:
            dctx.copy_stream(compressed_file, output_file)

def decompress_test_split(dataset_path, output_path=None):
    """Decompress all .mds.zstd files in the test split"""
    
    dataset_path = Path(dataset_path)
    test_path = dataset_path / "test"
    
    if not test_path.exists():
        print(f"Error: Test split not found at {test_path}")
        return False
    
    # Use output path or create a new directory
    if output_path is None:
        output_path = dataset_path.parent / f"{dataset_path.name}_decompressed"
    else:
        output_path = Path(output_path)
    
    output_test_path = output_path / "test"
    output_test_path.mkdir(parents=True, exist_ok=True)
    
    print(f"Decompressing test split from {test_path} to {output_test_path}")
    
    # Copy index.json if it exists
    index_file = test_path / "index.json"
    if index_file.exists():
        shutil.copy2(index_file, output_test_path / "index.json")
        print(f"Copied {index_file}")
    
    # Decompress all .mds.zstd files
    compressed_files = list(test_path.glob("*.mds.zstd"))
    
    if not compressed_files:
        print(f"No .mds.zstd files found in {test_path}")
        return False
    
    print(f"Found {len(compressed_files)} compressed files to decompress")
    
    for compressed_file in compressed_files:
        # Remove .zstd extension for output filename
        output_filename = compressed_file.name.replace('.zstd', '')
        output_file = output_test_path / output_filename
        
        try:
            decompress_file(compressed_file, output_file)
        except Exception as e:
            print(f"Error decompressing {compressed_file}: {e}")
            return False
    
    print(f"Successfully decompressed {len(compressed_files)} files")
    print(f"Decompressed test split available at: {output_test_path}")
    print(f"You can now use this path in your YAML config with streaming: false")
    
    return True

def main():
    parser = argparse.ArgumentParser(description="Decompress test split for non-streaming mode")
    parser.add_argument("dataset_path", help="Path to the dataset (e.g., /s2/mds_300BT_corpus/)")
    parser.add_argument("--output", "-o", help="Output path for decompressed dataset")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without actually doing it")
    
    args = parser.parse_args()
    
    if args.dry_run:
        dataset_path = Path(args.dataset_path)
        test_path = dataset_path / "test"
        compressed_files = list(test_path.glob("*.mds.zstd"))
        
        print(f"DRY RUN: Would decompress {len(compressed_files)} files from {test_path}")
        for f in compressed_files[:5]:  # Show first 5
            print(f"  - {f.name}")
        if len(compressed_files) > 5:
            print(f"  ... and {len(compressed_files) - 5} more files")
        
        if args.output:
            print(f"Output would go to: {args.output}")
        else:
            print(f"Output would go to: {dataset_path.parent / f'{dataset_path.name}_decompressed'}")
        
        return
    
    success = decompress_test_split(args.dataset_path, args.output)
    
    if success:
        print("\n" + "="*60)
        print("SUCCESS: Test split decompressed!")
        print("="*60)
        print("To use the decompressed dataset:")
        print("1. Update your YAML config to point to the decompressed path")
        print("2. Set streaming: false for the eval_loader")
        print("3. This will avoid distributed barrier issues")
    else:
        print("\n" + "="*60)
        print("FAILED: Could not decompress test split")
        print("="*60)
        exit(1)

if __name__ == "__main__":
    main()

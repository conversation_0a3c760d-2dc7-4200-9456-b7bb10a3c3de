#!/bin/bash

# Setup script for training with large datasets over SSHFS
# Run this before starting distributed training with the 300BT dataset

echo "Setting up environment for large dataset training..."

# NCCL optimizations for network-mounted storage and distributed barriers
export NCCL_TIMEOUT=10800  # 3 hour timeout (very conservative)
export NCCL_BLOCKING_WAIT=1  # Enable blocking wait for better reliability
export NCCL_ASYNC_ERROR_HANDLING=1  # Better error handling
export NCCL_DEBUG=INFO  # Enable debug logging to diagnose issues
export NCCL_IB_TIMEOUT=50  # Increase InfiniBand timeout significantly
export NCCL_SOCKET_NTHREADS=16  # Increase socket threads for better network handling
export NCCL_NSOCKS_PERTHREAD=8  # More sockets per thread
export NCCL_SOCKET_IFNAME=^docker,lo  # Exclude problematic interfaces
export NCCL_NET_GDR_LEVEL=0  # Disable GPU Direct RDMA if causing issues

# PyTorch distributed optimizations
export TORCH_DISTRIBUTED_DEBUG=DETAIL  # Enable detailed distributed debugging
export TORCH_NCCL_BLOCKING_WAIT=1  # Match NCCL setting
export TORCH_NCCL_ASYNC_ERROR_HANDLING=1  # Better error handling

# Memory and threading optimizations
export OMP_NUM_THREADS=4  # Limit OpenMP threads to prevent oversubscription
export MKL_NUM_THREADS=4  # Limit MKL threads
export KMP_AFFINITY=disabled  # Disable KMP affinity for better distributed performance
export MKL_THREADING_LAYER=sequential  # Use sequential threading for MKL

# Shared memory optimizations
export TMPDIR=/tmp  # Use /tmp instead of /dev/shm for temporary files
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512  # Limit CUDA memory fragmentation

# SSHFS optimizations (if you have control over the mount)
echo "SSHFS mount optimizations:"
echo "Consider remounting /s2 with these options for better performance:"
echo "sshfs user@server:/path /s2 -o cache=yes,kernel_cache,compression=no,large_read,big_writes,max_read=131072,max_write=131072"

# Clean up shared memory before training
echo "Cleaning up shared memory..."
python cleanup_shm.py --sudo --mkl 2>/dev/null || echo "cleanup_shm.py not found or failed - continuing anyway"

# Check dataset accessibility
echo "Checking dataset accessibility..."
if [ -d "/s2/mds_300BT_corpus/" ]; then
    echo "✓ Dataset directory accessible"
    # Check if we can list files (this will test SSHFS responsiveness)
    timeout 30 ls /s2/mds_300BT_corpus/ > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✓ Dataset directory responsive"
    else
        echo "⚠ Dataset directory slow to respond - consider SSHFS optimizations"
    fi
else
    echo "✗ Dataset directory not accessible: /s2/mds_300BT_corpus/"
    exit 1
fi

# Check available memory
echo "Checking system resources..."
free -h
echo "Available disk space in /tmp:"
df -h /tmp

echo ""
echo "Environment setup complete!"
echo ""
echo "Try these configurations in order of preference:"
echo ""
echo "1. FIRST TRY - Optimized config with non-streaming eval:"
echo "   python main.py yamls/main/flex-bert-modernbert-base-edu-fw-fw2-fw-300BT-tokenizer-65k.yaml"
echo ""
echo "2. SECOND TRY - Conservative config:"
echo "   python main.py yamls/main/flex-bert-modernbert-base-edu-fw-fw2-fw-300BT-tokenizer-65k-conservative.yaml"
echo ""
echo "3. LAST RESORT - No streaming (slowest but most stable):"
echo "   python main.py yamls/main/flex-bert-modernbert-base-edu-fw-fw2-fw-300BT-tokenizer-65k-no-streaming.yaml"
echo ""
echo "The key fix: eval_loader now uses streaming=false to avoid distributed barrier issues"

#!/usr/bin/env python3
"""
Calculate optimal intermediate_size for GLU layers in FlexBERT models.

This script helps determine the appropriate intermediate_size for GLU (Gated Linear Unit) layers,
taking into account the parameter overhead of GLU compared to standard MLP layers.
"""

def calculate_glu_intermediate_size(hidden_size: int, target_ratio: float = 4.0, glu_adjustment: bool = True) -> int:
    """
    Calculate intermediate_size for GLU layers.

    Args:
        hidden_size: The model's hidden dimension
        target_ratio: Target ratio of intermediate_size to hidden_size (default 4.0 for BERT)
        glu_adjustment: Whether to adjust for GLU parameter overhead

    Returns:
        Recommended intermediate_size
    """
    base_intermediate_size = int(hidden_size * target_ratio)

    if glu_adjustment:
        # GLU layers have ~1.5x parameters compared to standard MLP
        # To maintain similar parameter count, reduce intermediate_size by 2/3
        # This is mentioned in the BertResidualGLU docstring
        adjusted_size = int(base_intermediate_size * (2/3))
        return adjusted_size

    return base_intermediate_size


def analyze_model_sizes():
    """Analyze different model configurations and their intermediate_size recommendations."""

    print("GLU Intermediate Size Analysis")
    print("=" * 50)
    print()

    # Standard BERT configurations
    configs = [
        ("BERT-Base", 768, 12, 12),
        ("BERT-Large", 1024, 16, 24),
        ("Your Large Model", 1024, 16, 16),  # Your proposed config
    ]

    print("Standard BERT Ratios (4x hidden_size):")
    print("-" * 40)
    for name, hidden_size, num_heads, num_layers in configs:
        standard_intermediate = hidden_size * 4
        glu_adjusted = calculate_glu_intermediate_size(hidden_size, 4.0, True)

        print(f"{name}:")
        print(f"  Hidden size: {hidden_size}")
        print(f"  Attention heads: {num_heads}")
        print(f"  Layers: {num_layers}")
        print(f"  Standard intermediate_size: {standard_intermediate}")
        print(f"  GLU-adjusted intermediate_size: {glu_adjusted}")
        print(f"  GLU ratio to hidden_size: {glu_adjusted / hidden_size:.2f}x")
        print()

    print("Alternative Ratios for GLU:")
    print("-" * 30)
    hidden_size = 1024  # Your model

    ratios = [2.5, 3.0, 3.5, 4.0, 4.5]
    for ratio in ratios:
        glu_size = calculate_glu_intermediate_size(hidden_size, ratio, True)
        effective_ratio = glu_size / hidden_size
        print(f"  Target {ratio}x → GLU intermediate_size: {glu_size} ({effective_ratio:.2f}x)")

    print()
    print("Recommendations for your 1024 hidden_size model:")
    print("-" * 45)

    # Conservative (similar to current base models)
    conservative = calculate_glu_intermediate_size(1024, 3.0, True)
    print(f"Conservative: {conservative} (2.0x ratio)")

    # Standard (BERT-like)
    standard = calculate_glu_intermediate_size(1024, 4.0, True)
    print(f"Standard: {standard} (2.67x ratio)")

    # Aggressive (larger capacity)
    aggressive = calculate_glu_intermediate_size(1024, 5.0, True)
    print(f"Aggressive: {aggressive} (3.33x ratio)")

    print()
    print("Key Points:")
    print("- GLU layers use ~1.5x parameters vs standard MLP")
    print("- The commented intermediate_size: 1536 in your config ≈ 1.5x ratio")
    print("- This is quite conservative compared to BERT's 4x ratio")
    print("- For a large model, consider 2048-2730 range")


def estimate_parameters(hidden_size: int, intermediate_size: int, num_layers: int,
                       num_heads: int, vocab_size: int = 50000, use_glu: bool = True):
    """Estimate total parameters for a model configuration."""

    # Embedding parameters
    embedding_params = vocab_size * hidden_size

    # Per-layer parameters
    # Attention: QKV projection + output projection
    attn_params_per_layer = (hidden_size * hidden_size * 3) + (hidden_size * hidden_size)

    # MLP parameters
    if use_glu:
        # GLU: input projection (2x intermediate for gate), output projection
        mlp_params_per_layer = (hidden_size * intermediate_size * 2) + (intermediate_size * hidden_size)
    else:
        # Standard MLP: input projection, output projection
        mlp_params_per_layer = (hidden_size * intermediate_size) + (intermediate_size * hidden_size)

    # Layer norm parameters (assuming 2 per layer)
    norm_params_per_layer = hidden_size * 2

    total_layer_params = (attn_params_per_layer + mlp_params_per_layer + norm_params_per_layer) * num_layers

    # Final layer norm + output head
    final_params = hidden_size + (vocab_size * hidden_size)

    total_params = embedding_params + total_layer_params + final_params

    return {
        'total': total_params,
        'embedding': embedding_params,
        'layers': total_layer_params,
        'attention_per_layer': attn_params_per_layer,
        'mlp_per_layer': mlp_params_per_layer,
        'norm_per_layer': norm_params_per_layer,
        'final': final_params
    }


def calculate_modernbert_intermediate_size():
    """Calculate intermediate_size based on ModernBERT paper GLU Expansion values."""

    print("\n" + "=" * 60)
    print("ModernBERT Paper GLU Expansion Analysis")
    print("=" * 60)

    # From the ModernBERT paper table
    modernbert_configs = [
        ("ModernBERT-Base", 768, 2304),
        ("ModernBERT-Large", 1024, 5248),
    ]

    print("ModernBERT Paper Values:")
    print("-" * 30)
    for name, hidden_size, glu_expansion in modernbert_configs:
        intermediate_size = int(hidden_size * glu_expansion)
        print(f"{name}:")
        print(f"  Hidden size: {hidden_size}")
        print(f"  GLU Expansion: {glu_expansion}")
        print(f"  Calculated intermediate_size: {intermediate_size}")
        print(f"  Ratio to hidden_size: {intermediate_size / hidden_size:.3f}x")
        print()

    print("Recommendations for your 1024 hidden_size model:")
    print("-" * 50)

    # Your model with different GLU expansion values
    hidden_size = 1024
    glu_expansions = [2304, 3000, 4000, 5248, 6000]

    for expansion in glu_expansions:
        intermediate_size = int(hidden_size * expansion / 1000)  # Convert to reasonable size
        if expansion == 2304:
            note = " (ModernBERT-Base ratio)"
        elif expansion == 5248:
            note = " (ModernBERT-Large ratio)"
        else:
            note = ""

        print(f"GLU Expansion {expansion}: intermediate_size = {intermediate_size}{note}")

    print()
    print("CORRECTED Key Insight:")
    print("- The 'GLU Expansion' in ModernBERT paper appears to be intermediate_size directly")
    print("- ModernBERT-Base: 2304 intermediate_size with 768 hidden_size = 3.0x ratio")
    print("- ModernBERT-Large: 5248 intermediate_size with 1024 hidden_size = 5.125x ratio")
    print("- For your large model (1024 hidden), consider intermediate_size = 5248")


if __name__ == "__main__":
    analyze_model_sizes()
    calculate_modernbert_intermediate_size()

    print("\n" + "=" * 50)
    print("Parameter Estimation for Your Model")
    print("=" * 50)

    # Your proposed configuration
    hidden_size = 1024
    num_layers = 16
    num_heads = 16
    vocab_size = 65536  # Based on your tokenizer

    # Test different intermediate sizes including ModernBERT values
    intermediate_sizes = [1536, 2048, 2304, 3072, 5248]  # Added ModernBERT base and large values

    print(f"Model: {hidden_size}d, {num_layers}L, {num_heads}H, {vocab_size} vocab")
    print()

    for intermediate_size in intermediate_sizes:
        params = estimate_parameters(hidden_size, intermediate_size, num_layers, num_heads, vocab_size, True)
        ratio = intermediate_size / hidden_size

        note = ""
        if intermediate_size == 2304:
            note = " (ModernBERT-Base value)"
        elif intermediate_size == 5248:
            note = " (ModernBERT-Large value)"

        print(f"intermediate_size: {intermediate_size} ({ratio:.3f}x){note}")
        print(f"  Total parameters: {params['total']:,} ({params['total']/1e6:.1f}M)")
        print(f"  MLP params per layer: {params['mlp_per_layer']:,}")
        print(f"  Attention params per layer: {params['attention_per_layer']:,}")
        print()

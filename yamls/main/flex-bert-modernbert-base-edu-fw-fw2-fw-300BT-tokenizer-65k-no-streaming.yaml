# ModernBert Configuration - No Streaming (Most Conservative)
# This configuration completely avoids streaming to prevent distributed barrier issues
# Use this if you continue to get NCCL errors with the other configurations

data_local: /s2/mds_300BT_corpus/
data_remote: # If blank, files must be present in data_local

max_seq_len: 512
tokenizer_name: "/home/<USER>/notebooks/tokenizer_spm_hf_150B_EnFa"
mlm_probability: 0.3

# Run Name
run_name: modernbert-base-edu_fw-fw2-fw-300BT-tokenizer-65k-no-streaming

# Model (same as original)
model:
  name: flex_bert
  recompute_metric_loss: false
  pretrained_model_name: bert-base-uncased
  tokenizer_name: ${tokenizer_name}
  model_config:
    hidden_size: 768
    intermediate_size: 2304
    num_attention_heads: 12
    num_hidden_layers: 12
    attention_layer: rope
    attention_probs_dropout_prob: 0.0
    attn_out_bias: false
    attn_out_dropout_prob: 0.0
    attn_qkv_bias: false
    bert_layer: prenorm
    embed_dropout_prob: 0.0
    embed_norm: false
    final_norm: true
    embedding_layer: sans_pos
    loss_function: fa_cross_entropy
    loss_kwargs:
      reduction: mean
    mlp_dropout_prob: 0.0
    mlp_in_bias: false
    mlp_layer: glu
    mlp_out_bias: false
    normalization: rmsnorm
    norm_kwargs:
      eps: 1e-6
      bias: false
    padding: padded
    sparse_prediction: false
    hidden_act: gelu
    init_method: full_megatron
    init_std: 0.02
    init_cutoff_factor: 2.0
    init_small_embedding: false
    deterministic_fa2: false
    initial_attention_layer: null
    initial_bert_layer: null
    initial_mlp_layer: null
    num_initial_layers: 0
    skip_first_prenorm: true
    sliding_window: 128
    global_attn_every_n_layers: 3
    rotary_emb_base: 160000.0
    local_attn_rotary_emb_base: 10000.0
    unpad_embeddings: false
    pad_logits: false
    decoder_bias: true
    allow_embedding_resizing: true

# No streaming dataloaders - completely avoid distributed barriers
train_loader:
  name: text
  dataset:
    streaming: false  # No streaming to avoid distributed barrier issues
    local: ${data_local}
    remote: ${data_remote}
    split: train
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: true
    mlm_probability: ${mlm_probability}
  drop_last: true
  num_workers: 1  # Minimal workers
  pin_memory: false
  persistent_workers: false
  timeout: 1200  # 20 minute timeout
  prefetch_factor: 1

eval_loader:
  name: text
  dataset:
    streaming: false  # No streaming to avoid distributed barrier issues
    local: ${data_local}
    remote: ${data_remote}
    split: test
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: false
    mlm_probability: 0.15
  drop_last: false
  num_workers: 1  # Minimal workers
  pin_memory: false
  persistent_workers: false
  timeout: 1200  # 20 minute timeout
  prefetch_factor: 1

# Optimization
scheduler:
  name: warmup_stable_decay
  t_warmup: 0.025dur
  t_decay: 0.5dur
  alpha_f: 0.1

optimizer:
  name: decoupled_adamw
  lr: 8.0e-4
  betas:
  - 0.9
  - 0.98
  eps: 1.0e-06
  weight_decay: 1.0e-5
  filter_bias_norm_wd: true

algorithms:
  gradient_clipping:
    clipping_type: norm
    clipping_threshold: 1.0

max_duration: 100ba
eval_interval: 100ba

# Very conservative batch sizes
global_train_batch_size: 1024  # Very small
device_train_microbatch_size: 32  # Very small

# System with maximum timeouts
seed: 17
precision: amp_bf16
dist_timeout: 10800  # 3 hour timeout
find_unused_parameters: false

global_eval_batch_size: 128  # Very small
device_eval_microbatch_size: 32  # Very small
eval_subset_num_batches: 100  # Very few batches

# Logging
progress_bar: false
log_to_console: true
console_log_interval: 10ba

callbacks:
  speed_monitor:
    window_size: 50
  lr_monitor: {}
  memory_monitor: {}
  optimizer_monitor: {}
  dataloader_speed: {}

# ModernBert Configuration
# This configuration implements the ModernBert paper features:
# 1. Bias Terms Disabled: All linear layers except final decoder have bias=false
# 2. GeGLU Activation: Using GLU MLP layer for GeGLU-like behavior
# 3. Alternating Local/Global Attention: Every 3rd layer uses global attention (RoPE theta=160k)
#    Remaining layers use 128-token sliding window (RoPE theta=10k)
# 4. Trapezoidal Learning Rate: Warmup-Stable-Decay schedule with peak LR=8e-4
# 5. Batch Size Schedule: Warmup from 768 to 4096 over first 2.5% of training

# Follow the instructions in the README to set up ./my-copy-c4
# Or point data paths to your remote C4 dataset
# data_local: /s2/concatenated_mds_fw_fw2_50BT
data_local: /s2/mds_300BT_corpus/
data_remote: # If blank, files must be present in data_local

max_seq_len: 512
tokenizer_name: "/home/<USER>/notebooks/tokenizer_spm_hf_150B_EnFa" # switch to bert tokenizer until we add [MASK] token to the llama tokenizer meta-llama/Llama-2-7b-hf
mlm_probability: 0.3 # FlexBERT should use 30% masking for optimal performance

# Run Name
run_name: modernbert-base-edu_fw-fw2-fw-300BT-tokenizer-65k

# Model
model:
  name: flex_bert
  recompute_metric_loss: false # recompute metric loss, use if passing label_smoothing to record non-label-smoothed loss as a metric
  pretrained_model_name: bert-base-uncased
  tokenizer_name: ${tokenizer_name}
  # FlexBERT 'base' generally uses the default architecture values from the Hugging Face BertConfig object
  # Note: if using the pretrained_checkpoint argument to create a model from an existing checkpoint, make sure
  # the model_config settings match the architecture of the existing model
  model_config:
    hidden_size: 768
    intermediate_size: 2304
    num_attention_heads: 12 # bert-base default
    num_hidden_layers: 12 # bert-base default
    attention_layer: rope  # Using RoPE for alternating local/global attention
    attention_probs_dropout_prob: 0.0
    # Disable bias terms in all linear layers except final decoder
    attn_out_bias: false
    attn_out_dropout_prob: 0.0
    attn_qkv_bias: false
    bert_layer: prenorm
    embed_dropout_prob: 0.0
    embed_norm: false
    final_norm: true
    embedding_layer: sans_pos
    loss_function: fa_cross_entropy
    loss_kwargs:
      reduction: mean
    mlp_dropout_prob: 0.0
    mlp_in_bias: false
    mlp_layer: glu  # Using GLU (GeGLU-like) activation as specified
    mlp_out_bias: false
    normalization: rmsnorm
    norm_kwargs:
      eps: 1e-6
      bias: false  # Disable bias in LayerNorms
    padding: padded
    sparse_prediction: false
    hidden_act: gelu
    init_method: full_megatron
    init_std: 0.02
    init_cutoff_factor: 2.0
    init_small_embedding: false
    deterministic_fa2: false
    initial_attention_layer: null
    initial_bert_layer: null
    initial_mlp_layer: null
    num_initial_layers: 0
    skip_first_prenorm: true
    # Alternating local/global attention settings
    sliding_window: 128  # 128 token local sliding window
    global_attn_every_n_layers: 3  # Every third layer uses global attention
    rotary_emb_base: 160000.0  # RoPE theta for global attention layers
    local_attn_rotary_emb_base: 10000.0  # RoPE theta for local attention layers
    unpad_embeddings: false
    pad_logits: false
    # Enable bias only for final decoder layer
    decoder_bias: true
    allow_embedding_resizing: true

# Dataloaders
train_loader:
  name: text
  dataset:
    streaming: true  # Enable streaming for large datasets over network mounts
    local: ${data_local}
    remote: ${data_remote}
    split: train
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: true
    mlm_probability: ${mlm_probability}
    # # Network and timeout settings for SSHFS/large datasets
    # num_canonical_nodes: 32  # Reduce from default 128 for better distribution
    # cache_limit: 1073741824  # 1GB cache limit (in bytes)
    # partition_algo: "relaxed"  # More flexible partitioning for large datasets
  drop_last: true
  num_workers: 8
  # num_workers: 2  # Reduce workers to prevent resource exhaustion
  # pin_memory: false  # Disable pin_memory to reduce memory pressure
  # persistent_workers: false  # Disable to prevent memory leaks with large datasets
  # timeout: 300  # 5 minute timeout for dataloader workers
  # prefetch_factor: 1  # Reduce prefetching to save memory
  # sequence_packing: true  # Enable sequence packing (only works with streaming=false)
  # batch_size_warmup_min_size: 768
  # batch_size_warmup_tokens: 0.025dur

# eval_loader: DISABLED FOR TESTING - Skip eval to test train loader only
# eval_loader:
#   name: text
#   dataset:
#     streaming: true  # Must use streaming for compressed .mds.zstd files
#     local: ${data_local}
#     remote: ${data_remote}
#     split: test
#     tokenizer_name: ${tokenizer_name}
#     max_seq_len: ${max_seq_len}
#     shuffle: false
#     mlm_probability: 0.15 # We always evaluate at 15% masking for consistent comparison
#     # Ultra-conservative settings to minimize distributed barrier issues
#     download_retry: 3  # Fewer retries to fail fast
#     download_timeout: 120  # Shorter timeout to fail fast
#     predownload: 1000  # Very small predownload
#     num_canonical_nodes: 8  # Minimal nodes
#     cache_limit: 134217728  # 128MB cache limit (in bytes)
#     partition_algo: "orig"  # Use original partitioning
#     keep_zip: true  # Keep compressed files
#   drop_last: false  # Keep false for eval
#   num_workers: 1  # Minimal workers for eval
#   pin_memory: false  # Disable pin_memory to reduce memory pressure
#   persistent_workers: false  # Disable to prevent memory leaks
#   timeout: 600  # 10 minute timeout for dataloader workers
#   prefetch_factor: 1  # Reduce prefetching to save memory

# Optimization
# Trapezoidal Learning Rate schedule (Warmup-Stable-Decay)
scheduler:
  name: warmup_stable_decay
  t_warmup: 0.025dur  # Warmup for 2.5% of training
  t_decay: 0.5dur     # Decay for 50% of training (remaining 47.5% stable)
  alpha_f: 0.1        # Decay to 10% of peak LR

optimizer:
  name: decoupled_adamw
  lr: 8.0e-4  # Peak learning rate of 8e-4 as specified
  betas:
  - 0.9
  - 0.98
  eps: 1.0e-06
  weight_decay: 1.0e-5  # Amount of weight decay regularization
  filter_bias_norm_wd: true  # If True, doesn't apply weight decay to norm layers and biases

# Batch size scheduling is implemented in the dataloader via SequencePacker
# See train_loader dataset configuration for batch_size_warmup_* parameters
algorithms:
  gradient_clipping:
    clipping_type: norm
    clipping_threshold: 1.0

max_duration: 10ba #2ep
eval_interval: 0  # Disable evaluation to test train loader only

# Initial batch size (will be increased by the batch_size_schedule)
global_train_batch_size: 2048 #4096
device_train_microbatch_size: 32 #128

# System
seed: 17
precision: amp_bf16

# Distributed training optimizations for large datasets
dist_timeout: 3600  # 1 hour timeout for distributed operations
find_unused_parameters: false  # Optimize DDP performance

global_eval_batch_size: 512 #1024
device_eval_microbatch_size: 128 #256
eval_subset_num_batches: 512  # Reduce eval batches to speed up evaluation with large dataset

# Logging
progress_bar: false
log_to_console: true
console_log_interval: 10ba

callbacks:
  speed_monitor:
    window_size: 50
  lr_monitor: {}
  memory_monitor: {}
  optimizer_monitor: {}
  dataloader_speed: {}

debug: true
python_log_level: "DEBUG" 

# loggers:
#  tensorboard:
#    log_dir: ~/tb_runs/${run_name}
#    flush_interval: 100

#  wandb:
#    project: Modernbert_Pretrain
#   #  entity: sutkaggles

# # Checkpointing
# save_interval: 5000ba
# save_num_checkpoints_to_keep: 2
# save_folder: ./ckpt/{run_name}/
# # autoresume: true

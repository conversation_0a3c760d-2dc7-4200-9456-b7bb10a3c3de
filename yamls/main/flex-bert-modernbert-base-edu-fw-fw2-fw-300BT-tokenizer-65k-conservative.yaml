# ModernBert Configuration - Conservative settings for large datasets over SSHFS
# This configuration implements the ModernBert paper features with optimizations for:
# 1. Large datasets (300BT) over network mounts (SSHFS)
# 2. Distributed training stability
# 3. Memory efficiency and timeout handling

# Follow the instructions in the README to set up ./my-copy-c4
# Or point data paths to your remote C4 dataset
# data_local: /s2/concatenated_mds_fw_fw2_50BT
data_local: /s2/mds_300BT_corpus/
data_remote: # If blank, files must be present in data_local

max_seq_len: 512
tokenizer_name: "/home/<USER>/notebooks/tokenizer_spm_hf_150B_EnFa" # switch to bert tokenizer until we add [MASK] token to the llama tokenizer meta-llama/Llama-2-7b-hf
mlm_probability: 0.3 # FlexBERT should use 30% masking for optimal performance

# Run Name
run_name: modernbert-base-edu_fw-fw2-fw-300BT-tokenizer-65k-conservative

# Model (same as original)
model:
  name: flex_bert
  recompute_metric_loss: false
  pretrained_model_name: bert-base-uncased
  tokenizer_name: ${tokenizer_name}
  model_config:
    hidden_size: 768
    intermediate_size: 2304
    num_attention_heads: 12
    num_hidden_layers: 12
    attention_layer: rope
    attention_probs_dropout_prob: 0.0
    attn_out_bias: false
    attn_out_dropout_prob: 0.0
    attn_qkv_bias: false
    bert_layer: prenorm
    embed_dropout_prob: 0.0
    embed_norm: false
    final_norm: true
    embedding_layer: sans_pos
    loss_function: fa_cross_entropy
    loss_kwargs:
      reduction: mean
    mlp_dropout_prob: 0.0
    mlp_in_bias: false
    mlp_layer: glu
    mlp_out_bias: false
    normalization: rmsnorm
    norm_kwargs:
      eps: 1e-6
      bias: false
    padding: padded
    sparse_prediction: false
    hidden_act: gelu
    init_method: full_megatron
    init_std: 0.02
    init_cutoff_factor: 2.0
    init_small_embedding: false
    deterministic_fa2: false
    initial_attention_layer: null
    initial_bert_layer: null
    initial_mlp_layer: null
    num_initial_layers: 0
    skip_first_prenorm: true
    sliding_window: 128
    global_attn_every_n_layers: 3
    rotary_emb_base: 160000.0
    local_attn_rotary_emb_base: 10000.0
    unpad_embeddings: false
    pad_logits: false
    decoder_bias: true
    allow_embedding_resizing: true

# Conservative Dataloaders for large datasets over SSHFS
train_loader:
  name: text
  dataset:
    streaming: true  # Must use streaming for large datasets
    local: ${data_local}
    remote: ${data_remote}
    split: train
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: true
    mlm_probability: ${mlm_probability}
    # Very conservative network settings
    download_retry: 10  # More retries for unreliable network
    download_timeout: 600  # 10 minutes timeout
    predownload: 10000  # Much smaller predownload
    num_canonical_nodes: 32  # Fewer nodes for better reliability
    cache_limit: 100  # Very small cache
    partition_algo: "relaxed"
  drop_last: true
  num_workers: 2  # Minimal workers to reduce resource contention
  pin_memory: false
  persistent_workers: false
  timeout: 600  # 10 minute timeout
  prefetch_factor: 1

eval_loader:
  name: text
  dataset:
    streaming: true
    local: ${data_local}
    remote: ${data_remote}
    split: test
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: false
    mlm_probability: 0.15
    download_retry: 10
    download_timeout: 600
    predownload: 5000  # Even smaller for eval
    num_canonical_nodes: 32
    cache_limit: 50
    partition_algo: "relaxed"
  drop_last: false
  num_workers: 2
  pin_memory: false
  persistent_workers: false
  timeout: 600
  prefetch_factor: 1

# Optimization (same as original)
scheduler:
  name: warmup_stable_decay
  t_warmup: 0.025dur
  t_decay: 0.5dur
  alpha_f: 0.1

optimizer:
  name: decoupled_adamw
  lr: 8.0e-4
  betas:
  - 0.9
  - 0.98
  eps: 1.0e-06
  weight_decay: 1.0e-5
  filter_bias_norm_wd: true

algorithms:
  gradient_clipping:
    clipping_type: norm
    clipping_threshold: 1.0

max_duration: 100ba
eval_interval: 100ba

# Reduced batch sizes for stability
global_train_batch_size: 2048  # Reduced from 4096
device_train_microbatch_size: 64  # Reduced from 128

# System with extended timeouts
seed: 17
precision: amp_bf16
dist_timeout: 7200  # 2 hour timeout
find_unused_parameters: false

global_eval_batch_size: 256  # Reduced from 512
device_eval_microbatch_size: 64  # Reduced from 128
eval_subset_num_batches: 256  # Further reduced

# Logging
progress_bar: false
log_to_console: true
console_log_interval: 10ba

callbacks:
  speed_monitor:
    window_size: 50
  lr_monitor: {}
  memory_monitor: {}
  optimizer_monitor: {}
  dataloader_speed: {}

# ModernBert Configuration - Train Only (No Eval)
# Test configuration to isolate train loader issues from eval loader issues

data_local: /s2/mds_300BT_corpus/
data_remote: # If blank, files must be present in data_local

max_seq_len: 512
tokenizer_name: "/home/<USER>/notebooks/tokenizer_spm_hf_150B_EnFa"
mlm_probability: 0.3

# Run Name
run_name: modernbert-base-edu_fw-fw2-fw-300BT-tokenizer-65k-train-only

# Model (same as original)
model:
  name: flex_bert
  recompute_metric_loss: false
  pretrained_model_name: bert-base-uncased
  tokenizer_name: ${tokenizer_name}
  model_config:
    hidden_size: 768
    intermediate_size: 2304
    num_attention_heads: 12
    num_hidden_layers: 12
    attention_layer: rope
    attention_probs_dropout_prob: 0.0
    attn_out_bias: false
    attn_out_dropout_prob: 0.0
    attn_qkv_bias: false
    bert_layer: prenorm
    embed_dropout_prob: 0.0
    embed_norm: false
    final_norm: true
    embedding_layer: sans_pos
    loss_function: fa_cross_entropy
    loss_kwargs:
      reduction: mean
    mlp_dropout_prob: 0.0
    mlp_in_bias: false
    mlp_layer: glu
    mlp_out_bias: false
    normalization: rmsnorm
    norm_kwargs:
      eps: 1e-6
      bias: false
    padding: padded
    sparse_prediction: false
    hidden_act: gelu
    init_method: full_megatron
    init_std: 0.02
    init_cutoff_factor: 2.0
    init_small_embedding: false
    deterministic_fa2: false
    initial_attention_layer: null
    initial_bert_layer: null
    initial_mlp_layer: null
    num_initial_layers: 0
    skip_first_prenorm: true
    sliding_window: 128
    global_attn_every_n_layers: 3
    rotary_emb_base: 160000.0
    local_attn_rotary_emb_base: 10000.0
    unpad_embeddings: false
    pad_logits: false
    decoder_bias: true
    allow_embedding_resizing: true

# Train loader only - optimized for large dataset
train_loader:
  name: text
  dataset:
    streaming: true  # Enable streaming for large datasets over network mounts
    local: ${data_local}
    remote: ${data_remote}
    split: train
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: true
    mlm_probability: ${mlm_probability}
    # Conservative network settings for SSHFS/large datasets
    download_retry: 5  # Increase retries for network issues
    download_timeout: 300  # 5 minutes timeout for large files over SSHFS
    predownload: 50000  # Reduce predownload for memory efficiency
    num_canonical_nodes: 64  # Reduce from default 128 for better distribution
    cache_limit: 1073741824  # 1GB cache limit (in bytes)
    partition_algo: "relaxed"  # More flexible partitioning for large datasets
  drop_last: true
  num_workers: 4  # Reduce workers to prevent resource exhaustion
  pin_memory: false  # Disable pin_memory to reduce memory pressure
  persistent_workers: false  # Disable to prevent memory leaks with large datasets
  timeout: 300  # 5 minute timeout for dataloader workers
  prefetch_factor: 1  # Reduce prefetching to save memory

# NO EVAL LOADER - Testing train only

# Optimization
scheduler:
  name: warmup_stable_decay
  t_warmup: 0.025dur
  t_decay: 0.5dur
  alpha_f: 0.1

optimizer:
  name: decoupled_adamw
  lr: 8.0e-4
  betas:
  - 0.9
  - 0.98
  eps: 1.0e-06
  weight_decay: 1.0e-5
  filter_bias_norm_wd: true

algorithms:
  gradient_clipping:
    clipping_type: norm
    clipping_threshold: 1.0

max_duration: 20ba  # Short test run
eval_interval: 0  # Disable evaluation completely

# Conservative batch sizes
global_train_batch_size: 2048  # Reduced from 4096
device_train_microbatch_size: 64  # Reduced from 128

# System
seed: 17
precision: amp_bf16

# Distributed training optimizations for large datasets
dist_timeout: 3600  # 1 hour timeout for distributed operations
find_unused_parameters: false  # Optimize DDP performance

# Logging
progress_bar: false
log_to_console: true
console_log_interval: 1ba  # Log every batch for testing

callbacks:
  speed_monitor:
    window_size: 10  # Smaller window for testing
  lr_monitor: {}
  memory_monitor: {}
  optimizer_monitor: {}
  dataloader_speed: {}

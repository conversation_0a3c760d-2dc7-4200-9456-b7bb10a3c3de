# Large Dataset Training Guide: 300BT ModernBERT

This guide addresses the NCCL distributed training failure when scaling from 50B to 300B tokens over SSHFS.

## Problem Analysis

Your NCCL error when switching to the 300BT dataset is caused by:

1. **Network Mount Issues**: SSHFS can cause timeouts during distributed initialization
2. **Memory Pressure**: 6x larger dataset overwhelms default dataloader settings
3. **Default Timeout Settings**: Too aggressive for network-mounted storage
4. **Resource Contention**: Too many workers competing for network/memory resources

## Solution Overview

I've created two approaches with increasing conservatism:

### Approach 1: Optimized Configuration (Recommended First Try)

**File**: `yamls/main/flex-bert-modernbert-base-edu-fw-fw2-fw-300BT-tokenizer-65k.yaml` (modified)

**Key Changes**:
- ✅ **Streaming Mode**: Explicitly enabled for large datasets
- ✅ **Network Timeouts**: Increased to 300 seconds (5 minutes)
- ✅ **Retry Logic**: Increased to 5 retries for network issues
- ✅ **Memory Optimization**: Reduced workers (8→4), disabled pin_memory
- ✅ **Cache Management**: Limited cache sizes and predownload amounts
- ✅ **Distributed Timeouts**: Extended to 1 hour for large dataset initialization

### Approach 2: Conservative Configuration (Fallback)

**File**: `yamls/main/flex-bert-modernbert-base-edu-fw-fw2-fw-300BT-tokenizer-65k-conservative.yaml`

**Key Changes**:
- ✅ **Ultra-Conservative Settings**: 2 workers, 10-minute timeouts
- ✅ **Reduced Batch Sizes**: 4096→2048 global, 128→64 microbatch
- ✅ **Minimal Resource Usage**: Very small cache and predownload
- ✅ **Extended Timeouts**: Up to 2 hours for distributed operations

## Setup Instructions

### Step 1: Environment Setup

Run the setup script before training:

```bash
./setup_large_dataset_training.sh
```

This script:
- Sets NCCL timeout and debugging variables
- Optimizes memory and threading settings
- Cleans up shared memory
- Checks dataset accessibility
- Provides SSHFS optimization suggestions

### Step 2: Try Optimized Configuration

```bash
python main.py yamls/main/flex-bert-modernbert-base-edu-fw-fw2-fw-300BT-tokenizer-65k.yaml
```

### Step 3: If Still Failing, Try Conservative Configuration

```bash
python main.py yamls/main/flex-bert-modernbert-base-edu-fw-fw2-fw-300BT-tokenizer-65k-conservative.yaml
```

## Specific Answers to Your Questions

### 1. Is this NCCL error related to dataset size causing memory/resource issues?

**Yes**. The 6x larger dataset causes:
- Memory pressure during dataloader initialization
- Resource contention between workers
- Longer initialization times that exceed default timeouts

**Solutions Applied**:
- Reduced workers and memory usage
- Extended timeouts
- Enabled streaming mode for memory efficiency

### 2. Could SSHFS mount be causing network conflicts?

**Yes**. SSHFS can cause:
- Network timeouts during file access
- Connection aborts under heavy load
- Bandwidth bottlenecks with multiple workers

**Solutions Applied**:
- Increased download timeouts (60→300 seconds)
- Added retry logic (2→5 retries)
- Reduced concurrent workers to prevent bandwidth saturation
- Suggested SSHFS mount optimizations

### 3. Are there specific YAML parameters that need adjustment?

**Yes**. Key parameters modified:

```yaml
# Enable streaming for large datasets
streaming: true

# Network resilience
download_retry: 5
download_timeout: 300
num_canonical_nodes: 64

# Memory efficiency
num_workers: 4  # Reduced from 8
pin_memory: false
cache_limit: 1000
predownload: 50000  # Reduced from 100000

# Distributed training
dist_timeout: 3600  # 1 hour
```

### 4. Should you modify distributed training parameters?

**Yes**. Applied modifications:

```yaml
# Extended timeouts
dist_timeout: 3600
NCCL_TIMEOUT: 3600

# Better error handling
find_unused_parameters: false
NCCL_ASYNC_ERROR_HANDLING: 1

# Reduced batch sizes (conservative config)
global_train_batch_size: 2048  # From 4096
device_train_microbatch_size: 64  # From 128
```

## Troubleshooting

If you still encounter issues:

1. **Check SSHFS mount performance**:
   ```bash
   time ls /s2/mds_300BT_corpus/ | wc -l
   ```

2. **Monitor network usage during training**:
   ```bash
   iotop -a  # Check I/O usage
   nethogs   # Check network usage by process
   ```

3. **Try local copy** (if space permits):
   ```bash
   rsync -av /s2/mds_300BT_corpus/ /local/fast/storage/
   ```

4. **Further reduce resources** in conservative config:
   - `num_workers: 1`
   - `global_train_batch_size: 1024`
   - `download_timeout: 1200` (20 minutes)

## Expected Behavior

With these changes, you should see:
- ✅ Successful dataloader initialization
- ✅ No NCCL timeout errors
- ✅ Stable distributed training
- ⚠️ Potentially slower training due to conservative settings (acceptable trade-off)

The optimized configuration should work for most cases. Use the conservative configuration only if you continue to see failures.

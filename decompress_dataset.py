#!/usr/bin/env python3
"""
Quick script to decompress .mds.zstd files to .mds files
This allows using streaming=false to avoid distributed barrier issues
"""

import os
import zstandard as zstd
from pathlib import Path
import argparse
from tqdm import tqdm

def decompress_file(compressed_path, output_path):
    """Decompress a single .zstd file"""
    with open(compressed_path, 'rb') as compressed_file:
        dctx = zstd.ZstdDecompressor()
        with open(output_path, 'wb') as output_file:
            dctx.copy_stream(compressed_file, output_file)

def decompress_split(split_path, max_files=None):
    """Decompress all .mds.zstd files in a split directory"""
    split_path = Path(split_path)
    
    if not split_path.exists():
        print(f"Error: Split directory not found: {split_path}")
        return False
    
    # Find all compressed files
    compressed_files = list(split_path.glob("*.mds.zstd"))
    
    if not compressed_files:
        print(f"No .mds.zstd files found in {split_path}")
        return False
    
    # Limit files if specified
    if max_files:
        compressed_files = compressed_files[:max_files]
        print(f"Limiting to first {max_files} files for testing")
    
    print(f"Found {len(compressed_files)} compressed files to decompress in {split_path}")
    
    success_count = 0
    for compressed_file in tqdm(compressed_files, desc=f"Decompressing {split_path.name}"):
        # Create output filename (remove .zstd extension)
        output_file = compressed_file.with_suffix('')  # Remove .zstd
        
        # Skip if already exists
        if output_file.exists():
            print(f"Skipping {output_file.name} (already exists)")
            success_count += 1
            continue
        
        try:
            decompress_file(compressed_file, output_file)
            success_count += 1
        except Exception as e:
            print(f"Error decompressing {compressed_file}: {e}")
            return False
    
    print(f"Successfully decompressed {success_count}/{len(compressed_files)} files")
    return success_count == len(compressed_files)

def main():
    parser = argparse.ArgumentParser(description="Decompress MDS dataset for non-streaming mode")
    parser.add_argument("dataset_path", help="Path to dataset (e.g., /s2/mds_300BT_corpus/)")
    parser.add_argument("--split", choices=["train", "test", "both"], default="both", 
                       help="Which split to decompress")
    parser.add_argument("--max-files", type=int, help="Maximum files to decompress (for testing)")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done")
    
    args = parser.parse_args()
    
    dataset_path = Path(args.dataset_path)
    
    if not dataset_path.exists():
        print(f"Error: Dataset path not found: {dataset_path}")
        return
    
    splits_to_process = []
    if args.split in ["train", "both"]:
        splits_to_process.append("train")
    if args.split in ["test", "both"]:
        splits_to_process.append("test")
    
    for split in splits_to_process:
        split_path = dataset_path / split
        
        if not split_path.exists():
            print(f"Warning: {split} split not found at {split_path}")
            continue
        
        compressed_files = list(split_path.glob("*.mds.zstd"))
        
        if args.dry_run:
            print(f"\nDRY RUN - {split} split:")
            print(f"  Path: {split_path}")
            print(f"  Compressed files: {len(compressed_files)}")
            if args.max_files:
                print(f"  Would process: {min(len(compressed_files), args.max_files)} files")
            else:
                print(f"  Would process: {len(compressed_files)} files")
            
            # Show first few files
            for f in compressed_files[:3]:
                output_name = f.name.replace('.zstd', '')
                exists = "EXISTS" if (split_path / output_name).exists() else "NEW"
                print(f"    {f.name} -> {output_name} ({exists})")
            
            if len(compressed_files) > 3:
                print(f"    ... and {len(compressed_files) - 3} more files")
        else:
            print(f"\nProcessing {split} split...")
            success = decompress_split(split_path, args.max_files)
            
            if success:
                print(f"✓ Successfully decompressed {split} split")
            else:
                print(f"✗ Failed to decompress {split} split")
                return
    
    if not args.dry_run:
        print(f"\n{'='*60}")
        print("SUCCESS: Dataset decompression complete!")
        print(f"{'='*60}")
        print("You can now use streaming: false in your YAML config")
        print("This will avoid distributed barrier issues with SSHFS")

if __name__ == "__main__":
    main()
